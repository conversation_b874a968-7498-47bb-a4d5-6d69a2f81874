<template>
  <view class="page-container">
    <view class="tips-container">
      <view class="tips-content">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/7b1b021a2b9b4d3c8e2e311dc1a65b56.png"
          mode="scaleToFill"
          class="tips-img"
        />

        <uni-notice-bar
          background-color="transparent"
          color="#FA4D48"
          single
          scrollable
          class="tips-text"
          text="温馨提示:所有贷款在未放成功放款前，绝不收取任何费用"
        />
      </view>
    </view>

    <view class="amount">
      <view class="title">借款金额(元)</view>
      <view class="input-container">
        <input
          placeholder="请输入金额"
          v-model="form.demandAmount"
          @blur="demandAmountBlur"
          @focus="demandAmountFocus"
        />
        <view class="change-text-box">
          <view class="change-text">(金额可修改)</view>
          <img
            @click="clear"
            class="close-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/e4cdac0bcf9f4ef4a2da2633868835f4.png"
          />
        </view>
      </view>

      <view class="amount-bottom">
        <view>
          <view class="text">最高可借(元)</view>
          <view class="number">200000 </view>
        </view>
        <!-- <view class="line"></view> -->
        <!-- <view> <view class="text">利息总额(元)</view> <view class="number">222 </view> </view> -->
      </view>
    </view>

    <view class="borrowing-options">
      <view class="arrow-box">
        <view class="arrow"></view>
      </view>
      <view class="option term">
        <view class="label">还款期数</view>
        <picker
          range-key="label"
          :range="monthRange"
          :value="form.monthIndex"
          @change="monthPickerChange"
        >
          <view class="value">
            <view>{{ monthRange[this.form.monthIndex].value }}个月</view>
            <view class="recommend" v-if="monthRange[this.form.monthIndex].value === 12">推荐</view>
            <uni-icons
              style="margin-left: 15rpx"
              color="#A2A3A5"
              type="right"
              size="16"
            ></uni-icons>
          </view>
        </picker>
      </view>
      <!-- <view class="option-line"></view> -->
      <view class="option repayment-method">
        <view class="label">还款计划</view>
        <view class="value">
          <view class="repayment-amount">
            每月约应还
            <text class="amount-number">￥{{ monthlyPay }}</text>
          </view>
          <uni-icons style="margin-left: 15rpx" color="#A2A3A5" type="right" size="16"></uni-icons>
          <!-- <view class="repayment-reminder">实际贷款利息及放款金额以最终审批为准</view> -->
        </view>
      </view>
      <view class="option coupon">
        <view class="label">还款方式</view>
        <view class="value">
          <view class="coupon-container"> 随借随还 </view>
        </view>
      </view>

      <view class="tip"> *参考年化利率7.2%起,提前还款免剩余利息 </view>
      <view class="tip"> *具体额度利率以实际审批为准 </view>
    </view>

    <view class="phone-container">
      <input
        type="tel"
        maxlength="11"
        class="phone-input"
        placeholder="请输入手机号(已加密）"
        @blur="phoneBlur"
        v-model="form.phone"
        placeholder-style="color: #A2A3A5"
      />

      <!-- 人机验证组件 -->
      <aliyun-captcha
        v-show="shouldShowCaptcha"
        @success="onCaptchaSuccess"
        @fail="onCaptchaFail"
        @error="onCaptchaError"
      >
        <view class="get-my-quota">立即申请</view>
      </aliyun-captcha>

      <!-- 普通按钮 -->
      <view v-show="!shouldShowCaptcha" class="get-my-quota" @click="getMyQuotaHandler">立即申请</view>
    </view>

    <!-- <Declaration /> -->
    <img
      class="footer-img"
      src="https://cdn.oss-unos.hmctec.cn/common/path/4c71348c71fa4e34a206080c60e7cd11.png"
      alt=""
    />
    <view class="footer-text">
      郑重声明：本平台只提供贷款咨询和推荐服务， 放款由银行或金融机构进行，
      所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全，
      请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。证您的资金安全，
    </view>



    <RemindPopup ref="remindPopup" />
  </view>
</template>
<script>
import { validateMobile } from '@/utils/validators'
import AlertBar from '@/components/alert/AlertBar.vue'
import { reportUV, getDcProductLink } from '@/apis/common'
import { saveLoan, sendSmsCode } from '@/apis/common-2'
import { behaviorVerify } from '@/apis/user'
import { encryptByDES } from '@/utils/encrypt'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-hyh.vue'
import { formatAmount, parseAmount } from '@/utils/amount'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import Declaration from '@/components/footer/declaration-component/declaration-zxbc.vue'
import RemindPopup from '@/components/hyh/index/RemindPopup.vue'
import AliyunCaptcha from '@/components/captcha/aliyun-captcha.vue'

const fpPromise = FingerprintJS.load()

export default {
  name: 'template-v66',

  components: {
    AlertBar,
    Declaration,
    Header,
    RemindPopup,
    AliyunCaptcha
  },

  data() {
    return {
      form: {
        demandAmount: '50,000',
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        channelId: ''
      },
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      monthlyPay: '',
      isAgree: false,
      codeTimer: null,
      codeCountdown: 0,
      captchaVerifyParam: null
    }
  },

  computed: {
    shouldShowCaptcha() {
      return this.$u.test.mobile(this.form.phone);
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.codeTimer) {
      clearInterval(this.codeTimer)
      this.codeTimer = null
    }
  },

  mounted() {
    if (this.channelId) {
      this.form.channelId = this.channelId
      reportUV({ channelId: this.channelId })
    }
    this.updateAmountUI()
    uni.setNavigationBarTitle({
      title: '微钱管家'
    })
  },

  methods: {
    clickGetMyQuota() {
      throttle(this.getMyQuotaHandler)
    },

    getMyQuotaHandler() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
    },

    clickAgreement() {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    phoneBlur() {
      if (this.form.phone && !this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    },

    // 人机验证成功回调
    async onCaptchaSuccess(captchaVerifyParam) {
      this.captchaVerifyParam = captchaVerifyParam

      // 显示验证成功提示
      uni.showToast({
        title: '验证成功',
        icon: 'success',
        duration: 1500
      })

      // 先调用行为验证接口进行后端校验
      try {
        uni.showLoading({
          title: '验证中...',
          mask: true
        })

        const verifyRes = await behaviorVerify({
          phone: this.form.phone,
          template: 'v150',
          channelId: this.form.channelId,
          captchaVerifyParam
        })

        uni.hideLoading()

        if (verifyRes.code !== 200 || verifyRes.data !== true) {
          uni.showToast({
            title: '验证失败',
            icon: 'none'
          })
          this.resetCaptcha()
          return
        }

        // 验证通过后，直接进行登录
        this.login()
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '验证失败，请重试',
          icon: 'none'
        })
        this.resetCaptcha()
      }
    },

    // 人机验证失败回调
    onCaptchaFail(error) {
      uni.showToast({
        title: '验证失败，请重试',
        icon: 'none'
      })
      this.resetCaptcha()
    },

    // 人机验证错误回调
    onCaptchaError(error) {
      uni.showToast({
        title: '验证组件加载失败',
        icon: 'none'
      })
      this.resetCaptcha()
    },

    // 重置验证状态
    resetCaptcha() {
      this.captchaVerifyParam = null;
    },



    demandAmountSliderChange({ detail }) {
      const sliderValue = detail.value

      // 找出最近的预设值
      const closestPreset = this.presetValues.reduce((prev, curr) =>
        Math.abs(curr - sliderValue) < Math.abs(prev - sliderValue) ? curr : prev
      )

      // 更新金额
      const amountIndex = this.presetValues.indexOf(closestPreset)
      const amount = this.presetAmounts[amountIndex]
      this.form.demandAmount = formatAmount(amount)
      this.updateAmountUI()
    },

    clear() {
      this.form.demandAmount = '50000'
      uni.showToast({
        title: '最低借款金额为5万',
        icon: 'none'
      })
    },

    computedMonthPay() {
      let price = Number(parseAmount(this.form.demandAmount))
      let mLatte = (price * 12) / 100 / 12
      const month = this.monthRange[this.form.monthIndex].value

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },

    updateAmountUI() {
      this.setSliderValue(parseAmount(this.form.demandAmount))
      this.computedMonthPay()
    },

    demandAmountBlur() {
      const amount = parseInt(this.form.demandAmount)
      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.form.demandAmount = 50000
        this.updateAmountUI()
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.form.demandAmount = 50000
        this.updateAmountUI()
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.form.demandAmount = formatAmount(200000)
        this.updateAmountUI()
        return
      }

      this.form.demandAmount = formatAmount(amount)
      this.updateAmountUI()
    },

    demandAmountFocus() {
      this.form.demandAmount = parseAmount(this.form.demandAmount)
    },

    // 根据传入的金额，设置滑块的值
    setSliderValue(amount) {
      if (amount <= this.presetAmounts[0]) {
        this.form.demandAmountSlider = this.presetValues[0]
      } else if (amount >= this.presetAmounts[this.presetAmounts.length - 1]) {
        this.form.demandAmountSlider = this.presetValues[this.presetValues.length - 1]
      } else {
        for (let i = 1; i < this.presetAmounts.length; i++) {
          if (amount <= this.presetAmounts[i]) {
            const lowerAmount = this.presetAmounts[i - 1]
            const upperAmount = this.presetAmounts[i]
            const lowerValue = this.presetValues[i - 1]
            const upperValue = this.presetValues[i]

            const ratio = (amount - lowerAmount) / (upperAmount - lowerAmount)
            this.form.demandAmountSlider = lowerValue + ratio * (upperValue - lowerValue)
            break
          }
        }
      }
    },

    monthPickerChange({ detail }) {
      this.form.monthIndex = detail.value
      this.updateAmountUI()
    },


    async getDcProductLink(consumerId) {
      uni.showLoading({
        title: '加载中',
        mask: true
      })

      try {
        const res = await getDcProductLink({ consumerId })
        return res.data || ''
      } catch (error) {
        return ''
      } finally {
        uni.hideLoading()
      }
    },
    async getLoginParams() {
      const params = {}

      params.phoneBlack = getBlackPhone()
      params.deviceType = uni.getSystemInfoSync().platform
      params.demandAmount = parseAmount(this.form.demandAmount)
      params.phone = this.form.phone
      params.channelId = this.form.channelId

      return params
    },

    async login() {
      const params = await this.getLoginParams()

      const res = await saveLoan(params)

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        })
        return
      }

      // 登录成功后，记录手机号
      setBlackPhone(params.phone)
      this.$u.vuex('vuex_phone', params.phone)
      this.$u.vuex('vuex_consumerId', res.data)

      const urlParam = {
        consumerId: res.data,
        phone: this.form.phone,
        demandAmount: parseAmount(this.form.demandAmount),
        channelId: this.form.channelId,
        monthIndex: this.form.monthIndex
      }
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      const link = await this.getDcProductLink(urlParam.consumerId)
      this.$u.vuex('vuex_overloanWebview.url', link)
      if (link) {
        uni.navigateTo({
          url: `/extreme/v150/overloanWebview/index?param=${urlParamString}`
        })
      } else {
        uni.navigateTo({
          url: `/extreme/v150/wechatOfficialAccount/index?param=${urlParamString}`
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  background: #f6f6f8;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 438rpx;
    // background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/3d34ff5b56ae4f299c8ef6f00cd59fc2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: scale(3);
  }
}
.tips-container {
  padding: 10rpx;
  background: #fff8f8;
  .tips-content {
    display: flex;
    align-items: center;
    gap: 10rpx;
    background: #fff8f8;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    padding: 15rpx;

    .tips-img {
      width: 40rpx;
      height: 40rpx;
    }

    .tips-text {
      font-weight: 400;
      font-size: 28rpx;
      color: #fa4d48;
      line-height: 40rpx;
      margin: 0;
      padding: 0;
    }
  }
}

.alert {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 10rpx 30rpx;
  background-color: #fff7eb;

  .alert-icon {
    width: 51rpx;
    height: 29rpx;
  }

  .alert-notice {
    padding: 0;
    margin: 0;
  }
}

.platform {
  position: relative;
  padding: 30rpx;
  display: flex;
  align-items: flex-end;
  gap: 20rpx;

  .platform-name {
    color: #16283c;
    font-family: 'Alimama ShuHeiTi', serif;
    font-weight: bold;
    font-size: 40rpx;
  }

  .platform-feature {
    font-weight: 400;
    font-size: 24rpx;
    color: #4a5159;
    line-height: 35rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;

    .platform-feature-line {
      width: 1rpx;
      height: 19rpx;
      background: #4a5159;
    }
  }
}

.amount {
  position: relative;
  margin: 20rpx 30rpx 0 30rpx;
  background: linear-gradient(180deg, #fef2f2 0%, #f0d3d3 100%);
  padding: 45rpx 30rpx 140rpx 30rpx;
  border-radius: 20rpx 20rpx 0 0;
  .title {
    font-weight: normal;
    font-size: 28rpx;
    color: #171a1d;
    line-height: 39rpx;
  }

  .input-container {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    gap: 20rpx;

    input {
      font-family: DIN;
      font-weight: 700;
      font-size: 80rpx;
      color: #333333;
      line-height: 98rpx;
      flex: 1;
    }

    .change-text-box {
      display: flex;
      align-items: center;
      padding: 17rpx 25rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 34rpx;
      .close-icon {
        width: 30rpx;
        height: 30rpx;
        margin-left: 15rpx;
      }
    }
  }

  .amount-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.53) 0%, rgba(216, 216, 216, 0) 100%);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10rpx 40rpx 27rpx 40rpx;
    color: #171a1d;
    .line {
      height: 71rpx;
      width: 1rpx;
      border-right: 1rpx solid rgba(247, 60, 53, 0.2);
    }
    .text {
      font-size: 28rpx;
    }
    .number {
      font-size: 32rpx;
    }
  }

  .annual-interest-rate {
    margin-top: 35rpx;
    display: flex;
    align-items: center;
    gap: 15rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 32rpx;

    .tag {
      padding: 8rpx 18rpx;
      background: linear-gradient(270deg, #ffcd85 0%, #ffab44 100%);
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 25rpx;
      flex-shrink: 0;
      border-radius: 14rpx;
    }

    .highlight {
      color: #ffc93b;
    }
  }
}
.arrow-box {
  display: flex;
  justify-content: center;
  background: #fff;
  .arrow {
    width: 0;
    height: 0;
    border-top: 20rpx solid #fad2d2;
    border-right: 20rpx solid transparent;
    border-left: 20rpx solid transparent;
  }
}
.borrowing-options {
  padding: 0 0 15rpx 0;
  margin: 0 30rpx;
  background: #ffffff;
  .tip {
    color: #999999;
    font-size: 24rpx;
    line-height: 36rpx;
    padding: 5rpx 30rpx;
  }
  .option-line {
    margin-left: 60rpx;
    height: 2rpx;
    background-color: #f6f6f6;
  }

  .option {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    .label {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 46rpx;
    }

    &.term .value {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
      display: flex;
      align-items: center;

      .recommend {
        margin-left: 3rpx;
        padding: 7rpx 10rpx;
        background: #ff3636;
        border-radius: 203rpx 203rpx 203rpx 203rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #eef9f3;
        line-height: 18rpx;
      }
    }

    &.repayment-method .value {
      display: flex;
      align-items: flex-end;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      .amount-number {
        color: #333333;
      }

      .repayment-reminder {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 29rpx;
      }
    }

    &.coupon .value {
      gap: 15rpx;
      display: flex;
      align-items: center;

      .coupon-container {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 32rpx;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .days {
          color: #edbf7c;
        }

        .coupon-tips {
          padding: 8rpx 11rpx;
          background: #fff0f5;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #ff5e7c;
          line-height: 21rpx;
        }
      }
    }
  }
}

.phone-container {
  margin: 0 30rpx;
  padding: 40rpx 20rpx 36rpx;
  background: #ffffff;
  border-radius: 0 0 16rpx 16rpx;

  .phone-input {
    height: 96rpx;
    padding: 30rpx 40rpx;
    background: #f6f6f8;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
    font-size: 36rpx;
    border: 1px solid #f73c35;
  }

  .get-my-quota {
    margin: 20rpx 0;
    padding: 22rpx;
    text-align: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    background: #f73c35;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
  }

  .agreement {
    display: flex;
    gap: 5rpx;

    .agree-icon {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
    }

    .agreement-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 32rpx;

      .name {
        color: $color-primary;
      }
    }
  }
}
.footer-img {
  width: 100%;
  margin-top: 20rpx;
}
.footer-text {
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: #939393;
  line-height: 34rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
  padding: 0 60rpx;
  margin-top: 30rpx;
}

.declaration {
  padding: 0 60rpx;
}

.agreement-popup {
  padding: 40rpx 50rpx 80rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/05f2447ceb6b45caaf742b8366205959.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
    //font-size: 26rpx;
    //color: #3D3D3D;
    //line-height: 42rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}


</style>
